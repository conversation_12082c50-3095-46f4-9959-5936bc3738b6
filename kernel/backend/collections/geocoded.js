export default {
    name: 'geocoded',
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        address: {
            type: 'string',
            index: true
        },
        lat: {
            type: 'number',
            index: true
        },
        lng: {
            type: 'number',
            index: true
        },
        countryId: {
            type: 'string',
            required: false
        },
        countryCode: {
            type: 'string',
            required: false
        },
        countryName: {
            type: 'string',
            required: false
        },
        state: {
            type: 'string',
            required: false
        },
        city: {
            type: 'string',
            required: false
        },
        district: {
            type: 'string',
            required: false
        },
        subDistrict: {
            type: 'string',
            required: false
        },
        street: {
            type: 'string',
            required: false
        },
        doorNumber: {
            type: 'string',
            required: false
        },
        apartmentNumber: {
            type: 'string',
            required: false
        },
        postalCode: {
            type: 'string',
            required: false
        }
    },
    publish(app, data, context) {
        return false;
    }
};
