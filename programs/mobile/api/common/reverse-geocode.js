import _ from 'lodash';
import {GeoHash} from 'framework/helpers';

export default async function (ctx) {
    const app = ctx.app;
    const user = ctx.user;
    const {googleMapsApiKey, latitude, longitude} = ctx.payload;

    if (!_.isString(googleMapsApiKey) || !googleMapsApiKey.trim()) {
        return null;
    }

    if (!_.isNumber(latitude) || !_.isNumber(longitude)) {
        return null;
    }

    const locationHash = GeoHash.encode(latitude, longitude);
}
