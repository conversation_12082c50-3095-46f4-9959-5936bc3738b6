import integrations from '../integrations';

export default {
    name: 'check-store-transaction-statuses',
    repeatEvery: '*/30 * * * *',
    async action(app) {
        if (!app.hasModule('ecommerce')) {
            return;
        }

        const stores = await app.collection('ecommerce.stores').find({
            integrationType: {$in: ['trendyol', 'hepsiburada', 'pazarama', 'ciceksepeti', 'koctas', 'n11', 'boyner']},
            $select: ['_id']
        });

        for (const store of stores) {
            try {
                await integrations.checkStoreTransactionStatuses(app, store._id);
            } catch (error) {
                console.log(error.message);
            }
        }
    }
};
