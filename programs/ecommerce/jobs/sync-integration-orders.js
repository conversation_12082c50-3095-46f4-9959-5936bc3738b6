import integrations from '../integrations';

export default {
    name: 'sync-integration-orders',
    repeatEvery: '*/5 * * * *',
    async action(app) {
        if (!app.hasModule('ecommerce')) {
            return;
        }

        const stores = await app.collection('ecommerce.stores').find({
            integrationType: {
                $in: ['shopify', 'trendyol', 'hepsiburada', 'pazarama', 'pttavm', 'n11', 'ciceksepeti', 'koctas', 'boyner']
            },
            $select: ['_id']
        });

        for (const store of stores) {
            try {
                await integrations.importCanceledOrders(app, store._id);
            } catch (error) {
                console.log(error.message);
            }

            try {
                await integrations.importOrders(app, store._id);
            } catch (error) {
                console.log(error.message);
            }

            try {
                await integrations.importReturns(app, store._id);
            } catch (error) {
                console.log(error.message);
            }
        }
    }
};
