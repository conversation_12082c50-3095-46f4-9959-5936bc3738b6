{"app": {"name": "entererp", "title": "EnterERP", "locale": "tr", "timezone": "Europe/Istanbul", "version": "1.0.0", "packaged": false}, "database": {"mongodb": {"host": "127.0.0.1", "port": 27017, "db": "entererp"}, "postgresql": {"host": "127.0.0.1", "port": 5432, "user": "postgres", "password": "", "db": "entererp"}}, "mail": {"host": "", "port": 587, "secure": false, "auth": {"user": "", "password": ""}}, "server": {"protocol": "http", "host": "localhost", "port": 3000}, "deployment": {"protocol": "https", "host": "present.entererp.com", "port": 80}, "ssh": {"host": "present.entererp.com", "port": 22, "username": "root", "privateKey": "/home/<USER>/.ssh/id_rsa"}}